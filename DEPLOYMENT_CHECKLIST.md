# 🚀 Vercel Deployment Checklist

## ✅ Pre-Deployment Checklist

### 1. Code Structure
- [x] Created `vercel.json` configuration file
- [x] Restructured routes for serverless compatibility
- [x] Created `/api` directory with serverless functions
- [x] Fixed database connection management for serverless
- [x] Updated Redis configuration for serverless
- [x] Removed `process.exit()` calls from controllers
- [x] Added health check endpoint at `/api/health`

### 2. Required Files Created
- [x] `vercel.json` - Vercel configuration
- [x] `api/index.js` - Main API entry point
- [x] `api/health.js` - Health check endpoint
- [x] `api/docs.js` - API documentation endpoint
- [x] `api/auth/[...auth].js` - Authentication routes
- [x] `api/products/[...products].js` - Product routes
- [x] `api/orders/[...orders].js` - Order routes
- [x] `api/users/[...users].js` - User routes
- [x] `api/contact/[...contact].js` - Contact routes
- [x] `api/images/[...images].js` - Image routes
- [x] `api/video-blogs/[...video-blogs].js` - Video blog routes

### 3. Configuration Updates
- [x] Updated `package.json` with deployment scripts
- [x] Added Node.js engine specification
- [x] Created `.env.example` for environment variables
- [x] Updated database connection for connection pooling

## 🔧 Environment Variables Setup

### Required Variables
```bash
MONGO_URI=mongodb+srv://username:<EMAIL>/database
JWT_SECRET=your-super-secret-jwt-key
NODE_ENV=production
```

### Optional Variables
```bash
REDIS_URL=redis://default:password@hostname:port
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
CLIENT_URL=https://your-frontend-domain.com
```

## 🚀 Deployment Steps

### 1. Install Vercel CLI
```bash
npm install -g vercel
```

### 2. Login to Vercel
```bash
vercel login
```

### 3. Deploy to Vercel
```bash
# Initial deployment
vercel

# Production deployment
vercel --prod
```

### 4. Set Environment Variables
Option A - Via CLI:
```bash
vercel env add MONGO_URI
vercel env add JWT_SECRET
# ... add all required variables
```

Option B - Via Dashboard:
1. Go to Vercel dashboard
2. Select your project
3. Go to Settings > Environment Variables
4. Add all required variables

### 5. Redeploy with Environment Variables
```bash
vercel --prod
```

## 🧪 Testing Deployment

### 1. Health Check
```bash
curl https://your-app.vercel.app/api/health
```

Expected response:
```json
{
  "status": "success",
  "message": "Chinioti Wooden Art API is healthy and running",
  "services": {
    "database": { "status": "connected" },
    "redis": { "status": "connected" }
  }
}
```

### 2. API Documentation
Visit: `https://your-app.vercel.app/api/docs`

### 3. Test Key Endpoints
```bash
# Products
curl https://your-app.vercel.app/api/products

# User registration
curl -X POST https://your-app.vercel.app/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","email":"<EMAIL>","password":"password123","phone":"**********"}'
```

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Timeout**
   - Ensure MongoDB Atlas allows connections from `0.0.0.0/0`
   - Verify `MONGO_URI` is correctly formatted

2. **Function Timeout (30s limit)**
   - Optimize database queries
   - Add proper indexing to MongoDB collections
   - Use connection pooling (already implemented)

3. **Environment Variables Not Loading**
   - Redeploy after setting variables
   - Check variable names match exactly
   - Verify no typos in variable names

4. **CORS Issues**
   - Update `CLIENT_URL` to match frontend domain
   - Check CORS configuration in `vercel.json`

### Monitoring

- **Function Logs**: Check Vercel dashboard for errors
- **Health Endpoint**: Monitor `/api/health` regularly
- **Performance**: Monitor function execution times

## 📊 Performance Optimizations

### Already Implemented
- [x] Database connection caching
- [x] Redis caching for API responses
- [x] Optimized MongoDB connection settings
- [x] Proper error handling without process exits

### Additional Recommendations
- [ ] Implement rate limiting for production
- [ ] Add request/response compression
- [ ] Optimize image uploads for GridFS
- [ ] Add database query optimization

## 🔒 Security Considerations

### Implemented
- [x] Environment variables for secrets
- [x] CORS configuration
- [x] Input validation in routes
- [x] JWT token authentication

### Additional Recommendations
- [ ] Implement rate limiting
- [ ] Add request size limits
- [ ] Enable HTTPS only (handled by Vercel)
- [ ] Add API key authentication for admin routes

## 📈 Next Steps After Deployment

1. **Monitor Performance**
   - Check function execution times
   - Monitor database connection usage
   - Track error rates

2. **Update Frontend**
   - Update API base URL to Vercel deployment
   - Test all frontend integrations

3. **Set Up CI/CD**
   - Connect GitHub repository to Vercel
   - Enable automatic deployments

4. **Production Optimizations**
   - Implement caching strategies
   - Add monitoring and alerting
   - Set up backup procedures

## 📞 Support

If you encounter issues:
1. Check the health endpoint first
2. Review Vercel function logs
3. Verify environment variables are set
4. Test database connectivity from external tools

---

**Deployment Status**: ✅ Ready for Production Deployment
