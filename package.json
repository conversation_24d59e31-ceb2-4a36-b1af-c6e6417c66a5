{"name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Build completed'", "vercel-build": "echo 'Vercel build completed'", "deploy": "vercel --prod"}, "keywords": [], "author": "", "license": "ISC", "description": "", "engines": {"node": ">=18.0.0"}, "dependencies": {"@upstash/redis": "^1.34.9", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto-random-string": "^5.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "google-auth-library": "^9.15.1", "gridfs-stream": "^1.1.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "multer-gridfs-storage": "^5.0.2", "nodemailer": "^7.0.2", "redis": "^4.6.13", "server": "file:", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"nodemon": "^3.1.9"}}