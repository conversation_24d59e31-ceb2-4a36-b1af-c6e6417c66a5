const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const rateLimit = require("express-rate-limit");
const swaggerUi = require("swagger-ui-express");
const swaggerSpecs = require("./config/swagger");
const { connectDB } = require("./config/db");
const { connectRedis } = require("./config/redis");
const { connectGridFS } = require("./config/gridfs");
const authRoutes = require("./routes/authRoutes");
const userRoutes = require("./routes/userRoutes");
const productRouter = require("./routes/gridfsProductRoutes");
const orderRouter = require("./routes/orderRoutes");
const contactRoutes = require("./routes/contactRoutes");
const imageRoutes = require("./routes/imageRoutes");
const videoBlogRoutes = require("./routes/videoBlogRoutes");
// const { default: errorMiddleware } = require("./middleware/errorMiddleware");

const app = express();
dotenv.config();

// Connect to MongoDB
connectDB().then(() => {
  // Connect to GridFS after MongoDB connection is established
  connectGridFS().catch((err) => {
    console.error("Failed to connect to GridFS:", err);
    console.log("Continuing without GridFS...");
  });
});

// Connect to Redis
connectRedis().catch((err) => {
  console.error("Failed to connect to Redis:", err);
  console.log("Continuing without Redis caching...");
});

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    status: 'error',
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to all requests
app.use(limiter);

// Security headers middleware
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  next();
});

app.use(
  cors({
    origin: "*",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json({ limit: '10mb' })); // Add size limit

// Swagger API Documentation
app.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpecs, { explorer: true })
);

// Root route - redirect to API docs
app.get("/", (_, res) => {
  res.json({
    message: "Chinioti Wooden Art is Healthy",
    documentation: `${process.env.DEPLOYED_URL}}/api-docs`,
  });
});

// Auth callback fallback route (in case someone hits backend instead of frontend)
app.get("/auth-callback", (req, res) => {
  const { token, error, errorMessage } = req.query;

  if (error) {
    res.status(400).json({
      status: "error",
      message: errorMessage || "Authentication failed",
    });
  } else if (token) {
    res.json({
      status: "success",
      message: "Authentication successful",
      token,
      note: "This is the backend auth-callback endpoint. You should be redirected to the frontend.",
    });
  } else {
    res.status(400).json({
      status: "error",
      message: "Invalid auth callback request",
    });
  }
});

app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/products", productRouter);
app.use("/api/orders", orderRouter);
app.use("/api/contact", contactRoutes);
app.use("/api/images", imageRoutes);
app.use("/api/video-blogs", videoBlogRoutes);

// app.use(errorMiddleware);
const PORT = process.env.PORT || 5002;
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
