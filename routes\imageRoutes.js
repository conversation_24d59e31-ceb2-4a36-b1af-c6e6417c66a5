const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const rateLimit = require('express-rate-limit');
const {
  findFileById,
  createReadStream,
  findFileByFilename
} = require('../config/gridfs');

// Rate limiting for image requests to prevent abuse
const imageRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    status: 'error',
    message: 'Too many image requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to all image routes
router.use(imageRateLimit);

// Get image by filename
router.get('/file/:filename', async (req, res) => {
  try {
    // Validate filename to prevent directory traversal attacks
    const filename = req.params.filename;
    if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        status: 'fail',
        message: 'Invalid filename'
      });
    }

    const file = await findFileByFilename(filename);

    if (!file) {
      return res.status(404).json({
        status: 'fail',
        message: 'Image not found'
      });
    }

    // Set security headers
    res.set({
      'Content-Type': file.metadata.mimetype,
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY'
    });

    // Create a read stream and pipe it to the response
    const readStream = createReadStream(file._id);
    readStream.pipe(res);
  } catch (error) {
    console.error('Error retrieving image:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error retrieving image'
    });
  }
});

// Get image by ID
router.get('/id/:id', async (req, res) => {
  try {
    const id = req.params.id;

    // Validate if the ID is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Invalid image ID'
      });
    }

    const file = await findFileById(id);

    if (!file) {
      return res.status(404).json({
        status: 'fail',
        message: 'Image not found'
      });
    }

    // Set security headers
    res.set({
      'Content-Type': file.metadata.mimetype,
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY'
    });

    // Create a read stream and pipe it to the response
    const readStream = createReadStream(file._id);
    readStream.pipe(res);
  } catch (error) {
    console.error('Error retrieving image:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error retrieving image'
    });
  }
});

module.exports = router;
