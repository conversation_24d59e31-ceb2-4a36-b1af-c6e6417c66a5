const express = require('express');
const router = express.Router();
const OrderController = require('../controllers/orderController');
const { cacheMiddleware } = require('../config/redis');
const { protect, protectAdmin } = require('../middleware/authMiddleware');

const app = require('express')();

// Public route for creating orders
router.route('/').post(OrderController.createOrder);

// Admin-only routes
router.route('/').get(protectAdmin, cacheMiddleware(300), OrderController.getAllOrders);
router.route('/:id').get(protect, cacheMiddleware(300), OrderController.getOrder);
router.route('/:id').patch(protectAdmin, OrderController.updateOrder);
router.route('/:id').delete(protectAdmin, OrderController.deleteOrder);

module.exports = router;