/**
 * Redis configuration using Upstash Redis client
 * This provides better compatibility with serverless environments
 */
const { Redis } = require("@upstash/redis");
const { config } = require("dotenv");
config();

// Log Redis connection URL (without password for security)
const redisUrlParts = process.env.REDIS_URL
  ? process.env.REDIS_URL.split("@")
  : [];
if (redisUrlParts.length > 1) {
  console.log(`Connecting to Redis at: ${redisUrlParts[1]}`);
} else {
  console.log("Redis URL not properly configured");
}

// Parse the Redis URL to extract the necessary parts for Upstash REST client
function parseRedisUrl(url) {
  try {
    if (!url) return null;
    // Format: redis://default:password@hostname:port
    const match = url.match(/redis:\/\/(.+):(.+)@(.+):(\d+)/);
    if (!match) return null;

    // We only need the password and host
    const [, , password, host] = match;
    return {
      url: `https://${host}`,
      token: password,
    };
  } catch (error) {
    console.error("Error parsing Redis URL:", error.message);
    return null;
  }
}

// Create Redis client with Upstash REST API
const redisConfig = parseRedisUrl(process.env.REDIS_URL);
const redisClient = redisConfig
  ? new Redis({
      url: redisConfig.url,
      token: redisConfig.token,
    })
  : null;

// Connect to Redis - with Upstash REST client, no explicit connection is needed
const connectRedis = async () => {
  try {
    if (!redisClient) {
      console.log("Redis client not initialized - continuing without Redis");
      return false;
    }

    // Test connection by pinging the server with timeout
    const pingResult = await Promise.race([
      redisClient.ping(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Redis ping timeout')), 3000)
      )
    ]);

    if (pingResult === "PONG") {
      console.log("Redis client connected successfully");
      return true;
    } else {
      throw new Error("Redis ping failed");
    }
  } catch (error) {
    console.error("Redis connection error:", error);
    // Don't exit process, allow the app to function without Redis
    console.log("Continuing without Redis caching...");
    return false;
  }
};

// Cache middleware
const cacheMiddleware = (duration) => {
  return async (req, res, next) => {
    // Skip caching for non-GET requests
    if (req.method !== "GET") {
      return next();
    }

    // Create a unique cache key based on the request URL and query parameters
    const cacheKey = `api:${req.originalUrl || req.url}`;

    try {
      // Check if Redis client is available
      if (!redisClient) {
        return next();
      }

      // Try to get cached response
      const cachedResponse = await redisClient.get(cacheKey);

      if (cachedResponse) {
        // If cache hit, send cached response
        console.log(`Cache hit for ${cacheKey}`);
        let parsedResponse;
        try {
          // Check if the response is already an object
          parsedResponse =
            typeof cachedResponse === "object"
              ? cachedResponse
              : JSON.parse(cachedResponse);
        } catch (err) {
          console.error(`Error parsing cached response for ${cacheKey}:`, err);
          // If parsing fails, continue to the controller
          return next();
        }
        return res.status(200).json(parsedResponse);
      }

      // If cache miss, continue to the controller
      console.log(`Cache miss for ${cacheKey}`);

      // Store the original res.json method
      const originalJson = res.json;

      // Override res.json method to cache the response before sending
      res.json = function (data) {
        // Only cache successful responses
        if (res.statusCode === 200) {
          // Cache the response
          try {
            // Make sure data is properly stringified before caching
            const dataToCache =
              typeof data === "string" ? data : JSON.stringify(data);
            redisClient
              .setex(cacheKey, duration, dataToCache)
              .catch((err) =>
                console.error(`Error caching response for ${cacheKey}:`, err)
              );
          } catch (err) {
            console.error(`Error preparing data for cache ${cacheKey}:`, err);
          }
        }

        // Call the original json method
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error(`Cache middleware error for ${cacheKey}:`, error);
      next();
    }
  };
};

// Clear cache by pattern
const clearCache = async (pattern) => {
  try {
    if (!redisClient) {
      console.log("Redis not connected, skipping cache clear");
      return;
    }

    // Upstash Redis client doesn't support pattern matching with keys command directly
    // We'll use scan instead which is available in the Upstash REST API
    const keys = await redisClient.scan(0, { match: pattern, count: 100 });

    if (keys && keys[1] && keys[1].length > 0) {
      console.log(
        `Clearing ${keys[1].length} cache entries matching pattern: ${pattern}`
      );
      await Promise.all(keys[1].map((key) => redisClient.del(key)));
    } else {
      console.log(`No cache entries found matching pattern: ${pattern}`);
    }
  } catch (error) {
    console.error("Error clearing cache:", error);
  }
};

// Clear a specific cache key
const clearCacheKey = async (key) => {
  try {
    if (!redisClient) {
      console.log("Redis not connected, skipping cache clear");
      return;
    }

    const exists = await redisClient.exists(key);
    if (exists) {
      console.log(`Clearing cache key: ${key}`);
      await redisClient.del(key);
      return true;
    } else {
      console.log(`Cache key not found: ${key}`);
      return false;
    }
  } catch (error) {
    console.error(`Error clearing cache key ${key}:`, error);
    return false;
  }
};

module.exports = {
  redisClient,
  connectRedis,
  cacheMiddleware,
  clearCache,
  clearCacheKey,
};
