const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const User = require("../models/User");
const axios = require("axios");
const { sendResetPasswordEmail } = require("../utils/emailService");
require("dotenv").config();
if (!process.env.JWT_SECRET) {
  console.error("JWT_SECRET is not defined in environment variables");
  throw new Error("JWT_SECRET is required but not defined in environment variables");
}
exports.register = async (req, res) => {
  try {
    const { name, email, password, phone } = req.body;

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        status: "fail",
        message: "User already exists",
      });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const user = await User.create({
      name,
      email,
      password: hashedPassword,
      phone,
    });

    // Generate JWT token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: "24h",
    });

    res.status(201).json({
      status: "success",
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
        },
        token,
      },
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: "error",
      message: "Internal server error",
    });
  }
};

exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        status: "fail",
        message: "Invalid credentials",
      });
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        status: "fail",
        message: "Invalid credentials",
      });
    }

    // Generate JWT token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: "24h",
    });

    res.json({
      status: "success",
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
        },
        token,
      },
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: "error",
      message: "Internal server error",
    });
  }
};
exports.googleAuth = (req, res) => {
  console.log("Google Auth initiated");
  console.log("GOOGLE_CLIENT_ID:", process.env.GOOGLE_CLIENT_ID);
  console.log(
    "GOOGLE_REDIRECT_URI:",
    process.env.DEPLOYED_URL + process.env.GOOGLE_REDIRECT_URI
  );

  // Build the Google OAuth URL with all required parameters
  const url = new URL("https://accounts.google.com/o/oauth2/v2/auth");

  // Add required parameters
  url.searchParams.append("client_id", process.env.GOOGLE_CLIENT_ID);
  url.searchParams.append(
    "redirect_uri",
    process.env.DEPLOYED_URL + process.env.GOOGLE_REDIRECT_URI
  );
  url.searchParams.append("response_type", "code");
  url.searchParams.append("scope", "openid email profile");

  // Add optional parameters for better user experience
  url.searchParams.append("prompt", "select_account");
  url.searchParams.append("access_type", "offline");

  console.log("Redirecting to Google OAuth URL:", url.toString());
  res.redirect(url.toString());
};

exports.googleCallback = async (req, res) => {
  console.log("Google callback received");

  // Validate CLIENT_URL is set
  if (!process.env.CLIENT_URL) {
    console.error("CLIENT_URL environment variable is not set");
    return res.status(500).json({
      status: "error",
      message: "Server configuration error",
    });
  }

  const code = req.query.code;

  if (!code) {
    console.error("No authorization code received from Google");
    try {
      const errorUrl = new URL(`${process.env.CLIENT_URL}/auth-callback`);
      errorUrl.searchParams.append("error", "true");
      errorUrl.searchParams.append(
        "errorMessage",
        "No authorization code received from Google"
      );
      return res.redirect(errorUrl.toString());
    } catch (urlError) {
      console.error("Error creating redirect URL:", urlError);
      return res.status(400).json({
        status: "fail",
        message: "No authorization code received from Google",
      });
    }
  }

  console.log("Authorization code received:", code.substring(0, 10) + "...");

  try {
    console.log("Exchanging code for tokens...");
    // Exchange code for tokens
    const tokenResponse = await axios.post(
      "https://oauth2.googleapis.com/token",
      null,
      {
        params: {
          code,
          client_id: process.env.GOOGLE_CLIENT_ID,
          client_secret: process.env.GOOGLE_CLIENT_SECRET,
          redirect_uri:
            process.env.DEPLOYED_URL + process.env.GOOGLE_REDIRECT_URI,
          grant_type: "authorization_code",
        },
      }
    );

    console.log("Token exchange successful");
    const data = tokenResponse.data;

    if (!data.access_token) {
      throw new Error("No access token received from Google");
    }

    console.log("Access token received, fetching user profile...");

    // Get user info
    const profileResponse = await axios.get(
      "https://www.googleapis.com/oauth2/v2/userinfo",
      {
        headers: {
          Authorization: `Bearer ${data.access_token}`,
        },
      }
    );

    const profile = profileResponse.data;
    console.log("User profile received:", {
      email: profile.email,
      name: profile.name,
      id: profile.id,
    });

    // Find or create user
    let user = await User.findOne({ email: profile.email });
    if (!user) {
      // Generate a strong random password that matches your schema's requirements
      const randomPassword = Math.random().toString(36) + "Aa1!";

      // Sanitize the name to allow alphabets, spaces, and common name characters
      const sanitizedName = profile.name
        ? profile.name.replace(/[^\p{L}\p{M}\s\-'\.]/gu, "") // Allow letters from any language, spaces, hyphens, apostrophes, and periods
        : "User";

      // If sanitized name is empty or too short, use a default name
      const finalName =
        sanitizedName.trim().length < 2 ? "Google User" : sanitizedName;

      console.log("Original name from Google:", profile.name);
      console.log("Sanitized name:", sanitizedName);
      console.log("Final name to be used:", finalName);

      try {
        user = await User.create({
          name: finalName,
          email: profile.email,
          password: randomPassword, // Use the strong random password
          phone: "00000000000", // Placeholder
        });
      } catch (createError) {
        console.error("User creation error:", createError);
        // If there's still an error, use a completely safe name
        user = await User.create({
          name: "Google User",
          email: profile.email,
          password: randomPassword,
          phone: "00000000000",
        });
      }
    }

    // Generate JWT
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: "24h",
    });
    // Prepare user data for the frontend
    try {
      const redirectUrl = new URL(`${process.env.CLIENT_URL}/auth-callback`);

      // Add query parameters for authentication data
      redirectUrl.searchParams.append("token", token);
      redirectUrl.searchParams.append("userId", user._id.toString());
      redirectUrl.searchParams.append("name", user.name);
      redirectUrl.searchParams.append("email", user.email);
      redirectUrl.searchParams.append("phone", user.phone);

      // Redirect to the auth-callback page with authentication data
      res.redirect(redirectUrl.toString());
    } catch (urlError) {
      console.error("Error creating success redirect URL:", urlError);
      res.status(500).json({
        status: "error",
        message: "Authentication successful but redirect failed",
        token,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
        },
      });
    }
  } catch (err) {
    // Log detailed error information
    console.error("Google OAuth error details:");

    if (err.response) {
      console.error("Response data:", err.response.data);
      console.error("Response status:", err.response.status);
      console.error("Response headers:", err.response.headers);
    } else if (err.request) {
      console.error("Request error - no response received:", err.request);
    } else {
      console.error("Error message:", err.message);
    }

    console.error("Full error:", err);

    // Create a more descriptive error message
    let errorMessage = "Authentication failed. Please try again.";

    if (err.response?.data?.error_description) {
      errorMessage = `Google auth error: ${err.response.data.error_description}`;
    } else if (err.response?.data?.error) {
      errorMessage = `Google auth error: ${err.response.data.error}`;
    } else if (err.message) {
      errorMessage = `Error: ${err.message}`;
    }

    // Redirect to the auth-callback page with error information
    try {
      const errorUrl = new URL(`${process.env.CLIENT_URL}/auth-callback`);
      errorUrl.searchParams.append("error", "true");
      errorUrl.searchParams.append("errorMessage", errorMessage);

      res.redirect(errorUrl.toString());
    } catch (urlError) {
      console.error("Error creating error redirect URL:", urlError);
      res.status(500).json({
        status: "error",
        message: errorMessage,
      });
    }
  }
};

// Generate a random 6-digit OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Forgot Password - Send OTP to user's email
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({
        status: "fail",
        message: "No user found with that email address",
      });
    }

    // Generate OTP
    const otp = generateOTP();

    // Set reset token and expiration (15 minutes)
    user.resetPasswordOTP = otp;
    user.resetPasswordToken = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET
    );
    user.resetPasswordExpires = Date.now() + 15 * 60 * 1000; // 15 minutes
    await user.save();

    // Send email with OTP
    await sendResetPasswordEmail(user.email, otp);
    console.log("OTP sent");

    res.status(200).json({
      status: "success",
      message: "Password reset OTP sent to your email",
    });
  } catch (error) {
    console.error("Forgot password error:", error);
    res.status(500).json({
      status: "error",
      message: "Error sending password reset email",
    });
  }
};

// Verify OTP
exports.verifyOTP = async (req, res) => {
  try {
    const { email, otp } = req.body;

    // Find user by email
    const user = await User.findOne({
      email,
      resetPasswordOTP: otp,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({
        status: "fail",
        message: "Invalid or expired OTP",
      });
    }

    // Generate a reset token
    const resetToken = user.resetPasswordToken;

    res.status(200).json({
      status: "success",
      message: "OTP verified successfully",
      resetToken,
    });
  } catch (error) {
    console.error("Verify OTP error:", error);
    res.status(500).json({
      status: "error",
      message: "Error verifying OTP",
    });
  }
};

// Reset Password
exports.resetPassword = async (req, res) => {
  try {
    const { resetToken, password } = req.body;

    // Find user by reset token and check if token is not expired
    const user = await User.findOne({
      resetPasswordToken: resetToken,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({
        status: "fail",
        message: "Invalid or expired reset token",
      });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update user's password and clear reset fields
    user.password = hashedPassword;
    user.resetPasswordToken = null;
    user.resetPasswordOTP = null;
    user.resetPasswordExpires = null;
    await user.save();

    res.status(200).json({
      status: "success",
      message: "Password reset successful",
    });
  } catch (error) {
    console.error("Reset password error:", error);
    res.status(500).json({
      status: "error",
      message: "Error resetting password",
    });
  }
};

// Change Password
exports.changePassword = async (req, res) => {
  try {
    const { userId, currentPassword, newPassword } = req.body;

    // Find user by ID
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: "fail",
        message: "User not found",
      });
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(
      currentPassword,
      user.password
    );
    if (!isPasswordValid) {
      return res.status(401).json({
        status: "fail",
        message: "Current password is incorrect",
      });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update user's password
    user.password = hashedPassword;
    await user.save();

    res.status(200).json({
      status: "success",
      message: "Password changed successfully",
    });
  } catch (error) {
    console.error("Change password error:", error);
    res.status(500).json({
      status: "error",
      message: "Error changing password",
    });
  }
};
