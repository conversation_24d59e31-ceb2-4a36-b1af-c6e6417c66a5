/**
 * Test script to verify serverless setup
 * Run with: node test-serverless.js
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 Testing Serverless Setup...\n');

// Test 1: Check if required files exist
const requiredFiles = [
  'vercel.json',
  'api/index.js',
  'api/health.js',
  'api/auth/[...auth].js',
  'api/products/[...products].js',
  'api/orders/[...orders].js',
  'api/users/[...users].js',
  'config/db.js',
  'config/redis.js'
];

console.log('📁 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

// Test 2: Check vercel.json configuration
console.log('\n⚙️  Checking vercel.json configuration...');
try {
  const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
  
  if (vercelConfig.version === 2) {
    console.log('✅ Vercel version 2 configured');
  } else {
    console.log('❌ Incorrect Vercel version');
  }
  
  if (vercelConfig.builds && vercelConfig.builds.length > 0) {
    console.log('✅ Builds configuration present');
  } else {
    console.log('❌ No builds configuration');
  }
  
  if (vercelConfig.routes && vercelConfig.routes.length > 0) {
    console.log('✅ Routes configuration present');
  } else {
    console.log('❌ No routes configuration');
  }
} catch (error) {
  console.log('❌ Error reading vercel.json:', error.message);
}

// Test 3: Check package.json
console.log('\n📦 Checking package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (packageJson.engines && packageJson.engines.node) {
    console.log('✅ Node.js engine version specified');
  } else {
    console.log('⚠️  Node.js engine version not specified');
  }
  
  const requiredDeps = ['express', 'mongoose', 'cors', 'dotenv'];
  const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
  
  if (missingDeps.length === 0) {
    console.log('✅ All required dependencies present');
  } else {
    console.log('❌ Missing dependencies:', missingDeps.join(', '));
  }
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
}

// Test 4: Check environment variables template
console.log('\n🔐 Checking environment variables...');
const requiredEnvVars = [
  'MONGO_URI',
  'JWT_SECRET',
  'NODE_ENV'
];

console.log('Required environment variables:');
requiredEnvVars.forEach(envVar => {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar} - Set`);
  } else {
    console.log(`⚠️  ${envVar} - Not set (required for deployment)`);
  }
});

// Test 5: Check for serverless incompatible patterns
console.log('\n🚫 Checking for serverless incompatible patterns...');

// Check server.js for app.listen()
if (fs.existsSync('server.js')) {
  const serverContent = fs.readFileSync('server.js', 'utf8');
  if (serverContent.includes('app.listen(')) {
    console.log('⚠️  server.js contains app.listen() - this won\'t work in serverless');
    console.log('   Use api/index.js as the main entry point instead');
  } else {
    console.log('✅ No app.listen() found in server.js');
  }
}

// Summary
console.log('\n📊 Summary:');
if (allFilesExist) {
  console.log('✅ All required files are present');
  console.log('🚀 Ready for Vercel deployment!');
  console.log('\nNext steps:');
  console.log('1. Set environment variables in Vercel dashboard');
  console.log('2. Run: vercel --prod');
  console.log('3. Test the health endpoint: /api/health');
} else {
  console.log('❌ Some required files are missing');
  console.log('Please ensure all API route files are created');
}

console.log('\n📖 See VERCEL_DEPLOYMENT.md for detailed deployment instructions');
