const mongoose = require('mongoose');
const { redisClient } = require('../config/redis');
const { testConnection, getConnectionStatus } = require('../config/db');

module.exports = async (req, res) => {
  try {
    const healthCheck = {
      status: 'success',
      message: 'Chinioti Wooden Art API is healthy and running',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      services: {
        database: {
          status: 'unknown',
          connection: mongoose.connection.readyState,
          details: {}
        },
        redis: {
          status: 'unknown'
        }
      },
      deployment: {
        platform: 'Vercel',
        region: process.env.VERCEL_REGION || 'unknown',
        url: process.env.VERCEL_URL || process.env.DEPLOYED_URL || 'localhost'
      },
      debug: {
        mongoUri: !!process.env.MONGO_URI,
        nodeEnv: process.env.NODE_ENV
      }
    };

    // Test MongoDB connection actively
    try {
      console.log('Testing database connection...');
      const dbTest = await testConnection();
      const dbStatus = getConnectionStatus();

      healthCheck.services.database = {
        status: dbTest.success ? 'connected' : 'error',
        connection: dbStatus.state,
        message: dbTest.message,
        details: {
          name: dbStatus.name,
          host: dbStatus.host,
          readyState: dbStatus.status
        }
      };

      console.log('Database test result:', dbTest);
    } catch (error) {
      console.error('Database health check error:', error);
      healthCheck.services.database.status = 'error';
      healthCheck.services.database.error = error.message;
    }

    // Check Redis connection
    try {
      if (redisClient) {
        const pingResult = await Promise.race([
          redisClient.ping(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('timeout')), 2000)
          )
        ]);
        
        if (pingResult === 'PONG') {
          healthCheck.services.redis.status = 'connected';
        } else {
          healthCheck.services.redis.status = 'error';
        }
      } else {
        healthCheck.services.redis.status = 'not_configured';
      }
    } catch (error) {
      healthCheck.services.redis.status = 'error';
      healthCheck.services.redis.error = error.message;
    }

    // Set appropriate status code
    const allServicesHealthy = 
      healthCheck.services.database.status === 'connected' &&
      (healthCheck.services.redis.status === 'connected' || 
       healthCheck.services.redis.status === 'not_configured');

    const statusCode = allServicesHealthy ? 200 : 503;

    res.status(statusCode).json(healthCheck);
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Health check failed',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
};
