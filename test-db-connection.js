/**
 * Local test script to verify database connection works
 * This helps debug issues before deploying to Vercel
 */

require('dotenv').config();
const { connectDB, testConnection, getConnectionStatus } = require('./config/db');

async function testDatabaseConnection() {
  console.log('🔍 Testing Database Connection Locally...\n');
  
  // Check environment variables
  console.log('📋 Environment Check:');
  console.log('- MONGO_URI exists:', !!process.env.MONGO_URI);
  console.log('- MONGO_URI format:', process.env.MONGO_URI ? 
    (process.env.MONGO_URI.startsWith('mongodb') ? '✅ Valid' : '❌ Invalid') : 
    '❌ Missing');
  console.log('- NODE_ENV:', process.env.NODE_ENV || 'not set');
  console.log('');

  // Test initial connection
  console.log('🔌 Testing Initial Connection...');
  try {
    const connection = await connectDB();
    console.log('✅ Initial connection successful');
    console.log('- Database name:', connection.connection.name);
    console.log('- Connection state:', getConnectionStatus().status);
  } catch (error) {
    console.log('❌ Initial connection failed:', error.message);
    return;
  }

  // Test connection function
  console.log('\n🧪 Testing Connection Function...');
  try {
    const testResult = await testConnection();
    if (testResult.success) {
      console.log('✅ Connection test passed:', testResult.message);
    } else {
      console.log('❌ Connection test failed:', testResult.message);
    }
  } catch (error) {
    console.log('❌ Connection test error:', error.message);
  }

  // Test multiple connection attempts (simulating serverless)
  console.log('\n🔄 Testing Multiple Connection Attempts...');
  for (let i = 1; i <= 3; i++) {
    try {
      console.log(`Attempt ${i}:`);
      const result = await testConnection();
      console.log(`  ${result.success ? '✅' : '❌'} ${result.message}`);
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
    }
  }

  // Final status
  console.log('\n📊 Final Status:');
  const status = getConnectionStatus();
  console.log('- State:', status.status);
  console.log('- Database:', status.name || 'unknown');
  console.log('- Host:', status.host || 'unknown');

  console.log('\n✅ Local test completed. If this works, the issue is Vercel-specific.');
  
  // Close connection
  const mongoose = require('mongoose');
  await mongoose.connection.close();
  console.log('🔌 Connection closed.');
}

// Run the test
if (require.main === module) {
  testDatabaseConnection().catch(console.error);
}

module.exports = { testDatabaseConnection };
