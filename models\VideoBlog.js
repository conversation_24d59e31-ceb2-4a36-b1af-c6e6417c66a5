const mongoose = require('mongoose');

const videoBlogSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Video title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Video description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  // Legacy fields for file-based videos (optional for YouTube videos)
  videoFileId: {
    type: String,
    trim: true,
    // Required only for file-based videos, optional for YouTube videos
    required: false
  },
  videoFilename: {
    type: String,
    trim: true,
    // Required only for file-based videos, optional for YouTube videos
    required: function() {
      return !this.youtubeVideoId;
    }
  },
  mimetype: {
    type: String,
    trim: true,
    // Required only for file-based videos, optional for YouTube videos
    required: function() {
      return !this.youtubeVideoId;
    }
  },
  fileSize: {
    type: Number,
    min: [0, 'File size cannot be negative'],
    // Required only for file-based videos, optional for YouTube videos
    required: function() {
      return !this.youtubeVideoId;
    }
  },
  duration: {
    type: Number, // Duration in seconds
    default: null,
    min: [0, 'Duration cannot be negative']
  },
  thumbnailFileId: {
    type: String,
    default: null,
    trim: true
  },
  thumbnailFilename: {
    type: String,
    default: null,
    trim: true
  },
  // New YouTube-specific fields
  youtubeUrl: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        if (!v) return true; // Optional field
        return /^https?:\/\/(www\.)?(youtube\.com|youtu\.be)\/.+/.test(v);
      },
      message: 'YouTube URL must be a valid YouTube URL'
    }
  },
  youtubeVideoId: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        if (!v) return true; // Optional field
        return /^[a-zA-Z0-9_-]{11}$/.test(v);
      },
      message: 'YouTube video ID must be 11 characters long'
    }
  },
  category: {
    type: String,
    trim: true,
    default: 'General',
    maxlength: [50, 'Category cannot exceed 50 characters']
  },
  tags: {
    type: [String],
    default: [],
    validate: {
      validator: function(v) {
        return v.length <= 20; // Maximum 20 tags
      },
      message: 'Cannot have more than 20 tags'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  views: {
    type: Number,
    default: 0,
    min: [0, 'Views cannot be negative']
  },
  videoUrl: {
    type: String,
    required: [true, 'Video URL is required'],
    trim: true,
    validate: {
      validator: function(v) {
        return /^https?:\/\/.+/.test(v);
      },
      message: 'Video URL must be a valid HTTP/HTTPS URL'
    }
  },
  thumbnailUrl: {
    type: String,
    required: [true, 'Thumbnail URL is required'],
    trim: true,
    validate: {
      validator: function(v) {
        return /^https?:\/\/.+/.test(v);
      },
      message: 'Thumbnail URL must be a valid HTTP/HTTPS URL'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for better query performance
videoBlogSchema.index({ createdAt: -1 });
videoBlogSchema.index({ isActive: 1 });
videoBlogSchema.index({ title: 'text', description: 'text' });
videoBlogSchema.index({ videoUrl: 1 });
videoBlogSchema.index({ youtubeVideoId: 1 });
videoBlogSchema.index({ category: 1 });
videoBlogSchema.index({ tags: 1 });

// Transform the output to include id field
videoBlogSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    ret.id = ret._id;
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

videoBlogSchema.set('toObject', {
  virtuals: true,
  transform: function(doc, ret) {
    ret.id = ret._id;
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

// Pre-save middleware to update the updatedAt field
videoBlogSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('VideoBlog', videoBlogSchema);
