require('dotenv').config();
const mongoose = require('mongoose');
const { GridFSBucket } = require('mongodb');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB connected');
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const checkGridFSFiles = async () => {
  try {
    await connectDB();
    
    // Initialize GridFS bucket
    const gridFSBucket = new GridFSBucket(mongoose.connection.db, {
      bucketName: 'uploads'
    });

    console.log('Checking GridFS files...');
    
    // Get all files
    const files = await gridFSBucket.find({}).toArray();
    
    console.log(`Found ${files.length} files in GridFS:`);
    
    files.forEach((file, index) => {
      console.log(`${index + 1}. ${file.filename}`);
      console.log(`   ID: ${file._id}`);
      console.log(`   Size: ${file.length} bytes`);
      console.log(`   Upload Date: ${file.uploadDate}`);
      console.log(`   Metadata:`, file.metadata);
      console.log('---');
    });
    
    // Check for specific files mentioned in products
    const testFiles = [
      'image-1747203050705-703630688.png',
      'image-1748661850587-51bf864b3302258b235b9e784a3efbb2.png',
      'image-1747203330758-56026001.png'
    ];
    
    console.log('\nChecking for specific product image files:');
    for (const filename of testFiles) {
      const file = await gridFSBucket.find({ filename }).toArray();
      console.log(`${filename}: ${file.length > 0 ? 'FOUND' : 'NOT FOUND'}`);
    }
    
  } catch (error) {
    console.error('Error checking GridFS files:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

checkGridFSFiles();
