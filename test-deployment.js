/**
 * Simple test to verify the API structure works locally
 * This simulates how Vercel will handle the serverless functions
 */

const express = require('express');
const request = require('http');

// Test the health endpoint
async function testHealthEndpoint() {
  try {
    console.log('🔍 Testing health endpoint...');
    
    // Import the health function
    const healthHandler = require('./api/health');
    
    // Create mock request and response objects
    const mockReq = {
      method: 'GET',
      url: '/api/health'
    };
    
    const mockRes = {
      status: function(code) {
        this.statusCode = code;
        return this;
      },
      json: function(data) {
        console.log('✅ Health endpoint response:', JSON.stringify(data, null, 2));
        return this;
      },
      statusCode: 200
    };
    
    await healthHandler(mockReq, mockRes);
    
  } catch (error) {
    console.error('❌ Health endpoint test failed:', error.message);
  }
}

// Test the main API structure
function testAPIStructure() {
  console.log('📁 Testing API structure...');
  
  const fs = require('fs');
  const path = require('path');
  
  const apiDir = './api';
  const expectedFiles = [
    'health.js',
    'docs.js',
    'index.js',
    'auth/[...auth].js',
    'products/[...products].js',
    'orders/[...orders].js',
    'users/[...users].js',
    'contact/[...contact].js',
    'images/[...images].js',
    'video-blogs/[...video-blogs].js'
  ];
  
  let allFilesExist = true;
  
  expectedFiles.forEach(file => {
    const filePath = path.join(apiDir, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - MISSING`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

// Test configuration files
function testConfiguration() {
  console.log('⚙️  Testing configuration...');
  
  const fs = require('fs');
  
  // Test vercel.json
  try {
    const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
    console.log('✅ vercel.json is valid JSON');
    
    if (vercelConfig.version === 2) {
      console.log('✅ Vercel version 2 configured');
    } else {
      console.log('❌ Incorrect Vercel version');
    }
  } catch (error) {
    console.log('❌ vercel.json error:', error.message);
  }
  
  // Test package.json
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    console.log('✅ package.json is valid JSON');
    
    if (packageJson.engines && packageJson.engines.node) {
      console.log('✅ Node.js engine version specified');
    } else {
      console.log('⚠️  Node.js engine version not specified');
    }
  } catch (error) {
    console.log('❌ package.json error:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Serverless Deployment Tests\n');
  
  // Test 1: API Structure
  const structureOK = testAPIStructure();
  console.log('');
  
  // Test 2: Configuration
  testConfiguration();
  console.log('');
  
  // Test 3: Health Endpoint
  await testHealthEndpoint();
  console.log('');
  
  // Summary
  console.log('📊 Test Summary:');
  if (structureOK) {
    console.log('✅ API structure is correct');
    console.log('✅ Ready for Vercel deployment');
    console.log('\n🚀 Next steps:');
    console.log('1. Set environment variables in Vercel');
    console.log('2. Run: vercel --prod');
    console.log('3. Test deployed endpoints');
  } else {
    console.log('❌ API structure has issues');
    console.log('Please fix missing files before deployment');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testHealthEndpoint, testAPIStructure, testConfiguration };
