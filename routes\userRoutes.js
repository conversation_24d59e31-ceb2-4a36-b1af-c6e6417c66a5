const userController = require('../controllers/userController');
const express = require('express');
const router = express.Router();
const { cacheMiddleware } = require('../config/redis');
const { protectAdmin } = require('../middleware/authMiddleware');


const app = require('express')();

router.get('/:id', cacheMiddleware(300), userController.getUser);
router.get('/', cacheMiddleware(300), userController.getUsers);
router.put('/:id', userController.updateUser);
router.get('/', cacheMiddleware(300), userController.getUsers);

// Admin-only routes
router.route('/:id').delete(protectAdmin, userController.deleteUser)

module.exports = router;