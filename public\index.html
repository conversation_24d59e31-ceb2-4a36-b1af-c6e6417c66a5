<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CWA Backend API</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 { color: #fff; margin-bottom: 1rem; }
        .status { 
            background: rgba(76, 175, 80, 0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid #4CAF50;
        }
        .links {
            display: grid;
            gap: 1rem;
            margin-top: 2rem;
        }
        .link {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        .endpoint { font-family: monospace; font-size: 0.9em; opacity: 0.8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Chinioti Wooden Art - Backend API</h1>
        
        <div class="status">
            <strong>✅ API Status:</strong> Deployed and Running on Vercel Serverless
        </div>

        <p>Welcome to the CWA Backend API. This is a serverless Node.js/Express API deployed on Vercel.</p>

        <div class="links">
            <a href="/api/health" class="link">
                <strong>🔍 Health Check</strong>
                <div class="endpoint">GET /api/health</div>
            </a>
            
            <a href="/api/docs" class="link">
                <strong>📚 API Documentation</strong>
                <div class="endpoint">GET /api/docs</div>
            </a>
            
            <a href="/api/products" class="link">
                <strong>🛍️ Products API</strong>
                <div class="endpoint">GET /api/products</div>
            </a>
            
            <a href="/api/auth/register" class="link">
                <strong>🔐 Authentication</strong>
                <div class="endpoint">POST /api/auth/*</div>
            </a>
        </div>

        <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid rgba(255,255,255,0.2); font-size: 0.9em; opacity: 0.8;">
            <p><strong>Deployment:</strong> Vercel Serverless Functions</p>
            <p><strong>Version:</strong> 1.0.0</p>
            <p><strong>Last Updated:</strong> <span id="timestamp"></span></p>
        </div>
    </div>

    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        // Auto-redirect to health check after 5 seconds if no interaction
        setTimeout(() => {
            if (!document.hasFocus()) {
                window.location.href = '/api/health';
            }
        }, 5000);
    </script>
</body>
</html>
