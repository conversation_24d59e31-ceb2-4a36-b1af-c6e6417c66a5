const express = require('express');
const router = express.Router();
const ProductController = require('../controllers/gridfsProductController');
const { cacheMiddleware } = require('../config/redis');
const { uploadProductImages } = require('../middleware/gridfsUploadMiddleware');
const { protectAdmin } = require('../middleware/authMiddleware');

// Public routes
router.route('/').get(cacheMiddleware(300), ProductController.getAllProducts);
router.route('/:id').get(cacheMiddleware(300), ProductController.getProduct);

// Admin-only routes
router.route('/').post(protectAdmin, uploadProductImages, ProductController.createProduct);
router.route('/:id').patch(protectAdmin, uploadProductImages, ProductController.updateProduct);
router.route('/:id').delete(protectAdmin, ProductController.deleteProduct);

module.exports = router;
