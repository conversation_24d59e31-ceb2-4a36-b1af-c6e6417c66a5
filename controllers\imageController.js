const mongoose = require('mongoose');
const { GridFSBucket } = require('mongodb');

// Get image by filename
exports.getImageByFilename = async (req, res) => {
  try {
    console.log('Image request for filename:', req.params.filename);

    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('MongoDB not connected, readyState:', mongoose.connection.readyState);
      return res.status(500).json({
        status: 'error',
        message: 'Database not connected'
      });
    }

    // Initialize GridFS bucket directly
    const gridFSBucket = new GridFSBucket(mongoose.connection.db, {
      bucketName: 'uploads'
    });

    console.log('GridFS bucket initialized, looking for file:', req.params.filename);

    // Find the file
    const files = await gridFSBucket.find({ filename: req.params.filename }).toArray();

    if (files.length === 0) {
      console.log('File not found in GridFS:', req.params.filename);
      return res.status(404).json({
        status: 'fail',
        message: 'Image not found'
      });
    }

    const file = files[0];
    console.log('File found:', file.filename, 'mimetype:', file.metadata?.mimetype);

    // Set the appropriate content type
    res.setHeader('Content-Type', file.metadata?.mimetype || 'image/jpeg');

    // Create a read stream and pipe it to the response
    const readStream = gridFSBucket.openDownloadStream(file._id);

    readStream.on('error', (error) => {
      console.error('GridFS read stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          status: 'error',
          message: 'Error streaming image'
        });
      }
    });

    readStream.pipe(res);

  } catch (error) {
    console.error('Error retrieving image:', error);
    if (!res.headersSent) {
      res.status(500).json({
        status: 'error',
        message: 'Error retrieving image',
        error: error.message
      });
    }
  }
};

// Get image by ID
exports.getImageById = async (req, res) => {
  try {
    console.log('Image request for ID:', req.params.id);

    const id = req.params.id;

    // Validate if the ID is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Invalid image ID'
      });
    }

    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('MongoDB not connected, readyState:', mongoose.connection.readyState);
      return res.status(500).json({
        status: 'error',
        message: 'Database not connected'
      });
    }

    // Initialize GridFS bucket directly
    const gridFSBucket = new GridFSBucket(mongoose.connection.db, {
      bucketName: 'uploads'
    });

    console.log('GridFS bucket initialized, looking for file by ID:', id);

    // Find the file
    const files = await gridFSBucket.find({ _id: new mongoose.Types.ObjectId(id) }).toArray();

    if (files.length === 0) {
      console.log('File not found by ID:', id);
      return res.status(404).json({
        status: 'fail',
        message: 'Image not found'
      });
    }

    const file = files[0];
    console.log('File found by ID:', file.filename, 'mimetype:', file.metadata?.mimetype);

    // Set the appropriate content type
    res.setHeader('Content-Type', file.metadata?.mimetype || 'image/jpeg');

    // Create a read stream and pipe it to the response
    const readStream = gridFSBucket.openDownloadStream(file._id);

    readStream.on('error', (error) => {
      console.error('GridFS read stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          status: 'error',
          message: 'Error streaming image'
        });
      }
    });

    readStream.pipe(res);

  } catch (error) {
    console.error('Error retrieving image:', error);
    if (!res.headersSent) {
      res.status(500).json({
        status: 'error',
        message: 'Error retrieving image',
        error: error.message
      });
    }
  }
};
