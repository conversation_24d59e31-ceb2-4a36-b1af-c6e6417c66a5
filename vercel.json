{"version": 2, "functions": {"api/**/*.js": {"maxDuration": 30}}, "rewrites": [{"source": "/auth-callback", "destination": "/api/auth/callback"}, {"source": "/api-docs", "destination": "/api/docs"}, {"source": "/debug/db", "destination": "/api/debug/db"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS, PATCH"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}]}