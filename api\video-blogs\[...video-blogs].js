const { ensureConnection } = require("../../config/db");
const VideoBlogController = require("../../controllers/videoBlogController");
const { uploadVideoBlogFiles, handleUploadErrors } = require("../../middleware/videoBlogUploadMiddleware");
const { protect, requireAdmin } = require("../../middleware/authMiddleware");

// Helper function to apply middleware
const applyMiddleware = (middleware, req, res) => {
  return new Promise((resolve, reject) => {
    middleware(req, res, (err) => {
      if (err) reject(err);
      else resolve();
    });
  });
};

// Helper function to apply admin protection
const applyAdminProtection = async (req, res) => {
  await applyMiddleware(protect, req, res);
  await applyMiddleware(requireAdmin, req, res);
};

module.exports = async (req, res) => {
  try {
    // Ensure database connection
    await ensureConnection();
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Range');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    // Parse the URL to get the path segments
    const url = new URL(req.url, `http://${req.headers.host}`);
    const pathSegments = url.pathname.split('/').filter(segment => segment);

    // For catch-all routes in Vercel, we need to find the segments after 'video-blogs'
    const videoBlogsIndex = pathSegments.findIndex(segment => segment === 'video-blogs');
    const firstSegment = videoBlogsIndex >= 0 && pathSegments[videoBlogsIndex + 1] ? pathSegments[videoBlogsIndex + 1] : null;
    const secondSegment = videoBlogsIndex >= 0 && pathSegments[videoBlogsIndex + 2] ? pathSegments[videoBlogsIndex + 2] : null;
    
    console.log('Video-blogs API - Segments:', { firstSegment, secondSegment, pathSegments });
    
    if (firstSegment === 'stream' && secondSegment) {
      // GET /api/video-blogs/stream/:fileId
      if (req.method !== 'GET') {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed"
        });
      }
      req.params = { fileId: secondSegment };
      return await VideoBlogController.streamVideo(req, res);
    } else if (firstSegment === 'thumbnail' && secondSegment) {
      // GET /api/video-blogs/thumbnail/:fileId
      if (req.method !== 'GET') {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed"
        });
      }
      req.params = { fileId: secondSegment };
      return await VideoBlogController.getThumbnail(req, res);
    } else if (firstSegment && firstSegment !== 'stream' && firstSegment !== 'thumbnail') {
      // Individual video blog operations: /api/video-blogs/:id
      req.params = { id: firstSegment };
      
      if (req.method === 'GET') {
        // GET /api/video-blogs/:id
        return await VideoBlogController.getVideoBlog(req, res);
      } else if (req.method === 'PATCH') {
        // PATCH /api/video-blogs/:id - Admin only
        try {
          await applyAdminProtection(req, res);
          await applyMiddleware(uploadVideoBlogFiles, req, res);
          await applyMiddleware(handleUploadErrors, req, res);
          return await VideoBlogController.updateVideoBlog(req, res);
        } catch (error) {
          return res.status(500).json({
            status: "error",
            message: "Upload failed"
          });
        }
      } else if (req.method === 'DELETE') {
        // DELETE /api/video-blogs/:id - Admin only
        try {
          await applyAdminProtection(req, res);
          return await VideoBlogController.deleteVideoBlog(req, res);
        } catch (authError) {
          console.error("Admin protection error:", authError);
          return res.status(401).json({
            status: "error",
            message: "Unauthorized access"
          });
        }
      } else {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed for individual video blog"
        });
      }
    } else {
      // Collection operations: /api/video-blogs
      if (req.method === 'POST') {
        // POST /api/video-blogs - Create video blog - Admin only
        try {
          await applyAdminProtection(req, res);
          await applyMiddleware(uploadVideoBlogFiles, req, res);
          await applyMiddleware(handleUploadErrors, req, res);
          return await VideoBlogController.createVideoBlog(req, res);
        } catch (error) {
          return res.status(500).json({
            status: "error",
            message: "Upload failed"
          });
        }
      } else if (req.method === 'GET') {
        // GET /api/video-blogs
        return await VideoBlogController.getAllVideoBlogs(req, res);
      } else {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed"
        });
      }
    }
    
  } catch (error) {
    console.error("Video Blogs API error:", error);
    return res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: error.message
    });
  }
};
